<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 700px;
            margin: 0 auto;
            background-color: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 28px;
            font-weight: 700;
        }
        .header .emoji {
            font-size: 48px;
            margin-bottom: 15px;
            display: block;
        }
        .header .subtitle {
            font-size: 16px;
            opacity: 0.9;
            margin-top: 10px;
        }
        .content {
            padding: 40px 30px;
        }
        .success-message {
            background-color: #f0fdf4;
            border-left: 4px solid #10b981;
            padding: 20px;
            margin-bottom: 30px;
            border-radius: 0 8px 8px 0;
        }
        .success-message h2 {
            color: #065f46;
            margin: 0 0 10px 0;
            font-size: 20px;
        }
        .success-message p {
            color: #047857;
            margin: 0;
            font-size: 16px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        .stat-card {
            background-color: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
        }
        .stat-number {
            font-size: 32px;
            font-weight: 700;
            color: #1e40af;
            margin-bottom: 5px;
        }
        .stat-label {
            font-size: 14px;
            color: #64748b;
            font-weight: 500;
        }
        .department-stats {
            background-color: #fafafa;
            border-radius: 8px;
            padding: 25px;
            margin: 25px 0;
        }
        .department-stats h3 {
            color: #374151;
            margin: 0 0 20px 0;
            font-size: 18px;
            border-bottom: 2px solid #e5e7eb;
            padding-bottom: 10px;
        }
        .department-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
            gap: 15px;
        }
        .department-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background-color: white;
            padding: 12px 16px;
            border-radius: 6px;
            border: 1px solid #e5e7eb;
        }
        .department-name {
            font-weight: 500;
            color: #374151;
        }
        .department-count {
            background-color: #3b82f6;
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 600;
        }
        .highlight-box {
            background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
            border: 1px solid #93c5fd;
            border-radius: 8px;
            padding: 20px;
            margin: 25px 0;
            text-align: center;
        }
        .highlight-box .icon {
            font-size: 24px;
            margin-bottom: 10px;
        }
        .highlight-box h3 {
            color: #1e40af;
            margin: 0 0 10px 0;
        }
        .highlight-box p {
            color: #1e3a8a;
            margin: 0;
            font-size: 16px;
        }
        .footer {
            background-color: #f1f5f9;
            padding: 25px 30px;
            border-top: 1px solid #e2e8f0;
            text-align: center;
        }
        .footer p {
            margin: 5px 0;
            color: #64748b;
            font-size: 14px;
        }
        .footer .company {
            font-weight: 600;
            color: #374151;
        }
        .divider {
            height: 1px;
            background: linear-gradient(to right, transparent, #e2e8f0, transparent);
            margin: 30px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <span class="emoji">🎉</span>
            <h1>Qualitätsziele erreicht!</h1>
            <p class="subtitle">Alle Roten Karten erfolgreich bearbeitet</p>
        </div>

        <div class="content">
            <div class="success-message">
                <h2>Herzlichen Glückwunsch!</h2>
                <p>Alle roten Karten wurden erfolgreich bearbeitet. Es gibt derzeit keine offenen Qualitätsabweichungen mehr im System.</p>
            </div>

            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number">{{ $statistics['completed_cards'] }}</div>
                    <div class="stat-label">Bearbeitete Karten<br>insgesamt</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">{{ $statistics['recent_completed_30_days'] }}</div>
                    <div class="stat-label">Bearbeitet in den<br>letzten 30 Tagen</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">{{ $statistics['avg_processing_hours'] }}h</div>
                    <div class="stat-label">Durchschnittliche<br>Bearbeitungszeit</div>
                </div>
            </div>

            <div class="department-stats">
                <h3>📊 Bearbeitungsstatistik nach Abteilungen</h3>
                <div class="department-list">
                    @foreach($statistics['abteilungen'] as $abteilung => $anzahl)
                    <div class="department-item">
                        <span class="department-name">{{ $abteilung }}</span>
                        <span class="department-count">{{ $anzahl }}</span>
                    </div>
                    @endforeach
                </div>
            </div>

            <div class="highlight-box">
                <div class="icon">⏰</div>
                <h3>Bearbeitungsstand</h3>
                <p>Stand: {{ $statistics['completion_date'] }}</p>
            </div>

            <div class="divider"></div>

            <div style="text-align: center; color: #059669; font-size: 16px; font-weight: 500;">
                ✅ Alle Qualitätsabweichungen erfolgreich abgearbeitet<br>
                🎯 Qualitätsstandards vollständig eingehalten
            </div>
        </div>

        <div class="footer">
            <p class="company">Q-Leitstand | Analysenabweichung Management System</p>
            <p>Dies ist eine automatisch generierte Erfolgsmeldung.</p>
            <p>Weiter so! 💪</p>
        </div>
    </div>
</body>
</html>
