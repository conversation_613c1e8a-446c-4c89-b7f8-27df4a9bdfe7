<?php

namespace App\Services;

use App\Models\Rotekarte;
use App\Models\EmailVerteiler;
use App\Mail\AllRedCardsCompleteMail;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Carbon\Carbon;

class RedCardCompletionService
{
    /**
     * Prüft, ob alle roten Karten abgeschlossen sind und sendet eine Erfolgsmeldung
     */
    public function checkAndSendCompletionNotification(): void
    {
        try {
            // Prüfe, ob alle roten Karten abgeschlossen sind
            if ($this->areAllRedCardsCompleted()) {
                // Prüfe, ob bereits heute eine Erfolgsmeldung gesendet wurde
                $cacheKey = 'completion_email_sent_' . Carbon::now()->format('Y-m-d');

                if (!Cache::has($cacheKey)) {
                    Log::info('Alle roten Karten sind abgeschlossen - sende Erfolgsmeldung');
                    $this->sendCompletionEmail();

                    // Markiere, dass heute bereits eine E-Mail gesendet wurde (24h Cache)
                    Cache::put($cacheKey, true, Carbon::now()->endOfDay());
                } else {
                    Log::info('Alle roten Karten sind abgeschlossen, aber Erfolgsmeldung wurde heute bereits gesendet');
                }
            }
        } catch (\Exception $e) {
            Log::error('Fehler beim Prüfen der Rotekarten-Vollständigkeit: ' . $e->getMessage());
        }
    }

    /**
     * Prüft, ob alle roten Karten abgeschlossen sind
     */
    private function areAllRedCardsCompleted(): bool
    {
        // Zähle alle offenen roten Karten
        $offeneKarten = Rotekarte::where(function ($query) {
            $query->whereNull('qs_daten')
                  ->orWhere('qs_daten->status', 'Offen')
                  ->orWhere('qs_daten->status', 'In Bearbeitung')
                  ->orWhereRaw("JSON_TYPE(qs_daten) = 'NULL'");
        })->count();

        return $offeneKarten === 0;
    }

    /**
     * Sammelt Statistiken über die bearbeiteten roten Karten
     */
    private function getCompletionStatistics(): array
    {
        $totalCards = Rotekarte::count();
        $completedCards = Rotekarte::where('qs_daten->status', 'Abgeschlossen')->count();
        
        // Statistiken der letzten 30 Tage
        $last30Days = Carbon::now()->subDays(30);
        $recentCards = Rotekarte::where('created_at', '>=', $last30Days)->count();
        $recentCompleted = Rotekarte::where('created_at', '>=', $last30Days)
            ->where('qs_daten->status', 'Abgeschlossen')
            ->count();

        // Abteilungsstatistiken
        $abteilungsStats = [
            'Kleinguss (NG)' => Rotekarte::where('spektrometer_daten->abteilung', 'NG')
                ->where('qs_daten->status', 'Abgeschlossen')
                ->count(),
            'Grossguss (GG)' => Rotekarte::where('spektrometer_daten->abteilung', 'GG')
                ->where('qs_daten->status', 'Abgeschlossen')
                ->count(),
            'Handformerei (HF)' => Rotekarte::where('spektrometer_daten->abteilung', 'HF')
                ->where('qs_daten->status', 'Abgeschlossen')
                ->count(),
        ];

        // Durchschnittliche Bearbeitungszeit
        $avgProcessingTime = $this->calculateAverageProcessingTime();

        return [
            'total_cards' => $totalCards,
            'completed_cards' => $completedCards,
            'recent_cards_30_days' => $recentCards,
            'recent_completed_30_days' => $recentCompleted,
            'abteilungen' => $abteilungsStats,
            'avg_processing_hours' => $avgProcessingTime,
            'completion_date' => Carbon::now()->format('d.m.Y H:i'),
        ];
    }

    /**
     * Berechnet die durchschnittliche Bearbeitungszeit in Stunden
     */
    private function calculateAverageProcessingTime(): float
    {
        $completedCards = Rotekarte::where('qs_daten->status', 'Abgeschlossen')
            ->whereNotNull('created_at')
            ->whereNotNull('updated_at')
            ->get();

        if ($completedCards->isEmpty()) {
            return 0;
        }

        $totalHours = 0;
        $count = 0;

        foreach ($completedCards as $card) {
            $hours = $card->created_at->diffInHours($card->updated_at);
            $totalHours += $hours;
            $count++;
        }

        return $count > 0 ? round($totalHours / $count, 1) : 0;
    }

    /**
     * Sendet die Erfolgsmeldung an das Management
     */
    private function sendCompletionEmail(): void
    {
        try {
            // Hole Management E-Mail-Verteiler
            $managementEmails = EmailVerteiler::where('type', 'management')->get();

            if ($managementEmails->isEmpty()) {
                Log::warning('Keine E-Mail-Adressen für Management-Verteiler gefunden.');
                return;
            }

            // Sammle Statistiken
            $statistics = $this->getCompletionStatistics();

            // Teile E-Mails in TO und CC
            $toEmails = $managementEmails->where('is_cc', false)->pluck('email')->toArray();
            $ccEmails = $managementEmails->where('is_cc', true)->pluck('email')->toArray();

            // Sende E-Mail
            Mail::to($toEmails)
                ->cc($ccEmails)
                ->send(new AllRedCardsCompleteMail($statistics));

            Log::info('Erfolgsmeldung für vollständige Bearbeitung aller roten Karten an Management gesendet', [
                'to_emails' => $toEmails,
                'cc_emails' => $ccEmails,
                'statistics' => $statistics
            ]);

        } catch (\Exception $e) {
            Log::error('Fehler beim Senden der Erfolgsmeldung: ' . $e->getMessage());
        }
    }
}
