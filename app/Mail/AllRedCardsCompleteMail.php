<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class AllRedCardsCompleteMail extends Mailable
{
    use Queueable, SerializesModels;

    public array $statistics;

    /**
     * Create a new message instance.
     */
    public function __construct(array $statistics)
    {
        $this->statistics = $statistics;
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            subject: '🎉 Alle Roten Karten erfolgreich bearbeitet - Qualitätsziele erreicht!'
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: 'emails.all-red-cards-complete',
            with: [
                'statistics' => $this->statistics,
            ]
        );
    }

    /**
     * Get the attachments for the message.
     */
    public function attachments(): array
    {
        return [];
    }
}
